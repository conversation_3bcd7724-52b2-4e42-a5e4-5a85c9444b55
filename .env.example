# Zhi Deep Learning Mentor - Environment Variables (Example)
# 将本文件复制为 .env 并填入实际值。不要将 .env 提交到仓库。
# 前后端与CI会从该文件读取必要的配置，请保持键名一致。
#
# 快速开始（Python 3.12+ + uv）：
#   1) cp .env.example .env && 编辑必要配置
#   2) cd apps/backend && uv pip install -r requirements.txt
#   3) uv run uvicorn app.main:app --reload

# ----------------------------
# 基础应用配置
# ----------------------------
# 运行环境：development / staging / production
APP_ENV=development

# 日志级别：debug / info / warn / error
LOG_LEVEL=info

# 允许的前端来源（CORS），多个以逗号分隔
CORS_ORIGINS=http://localhost:5173

# ----------------------------
# Supabase（PostgreSQL / Auth / Storage）
# ----------------------------
SUPABASE_URL=
SUPABASE_ANON_KEY=
# 后端若需服务端权限操作（谨慎使用，避免暴露）
SUPABASE_SERVICE_ROLE_KEY=

# 数据库连接串（如不走 Supabase 直连，可单独提供）
# 例：postgresql+psycopg://user:password@host:5432/dbname
DATABASE_URL=

# ----------------------------
# （已精简移除）通用 LLM_* 与 LLM_PROVIDER 等变量不再需要
# 统一使用 OPENAI_API_KEY / OPENAI_BASE_URL / OPENAI_MODEL

# ----------------------------
# OpenAI（精简：仅三项必填）
# ----------------------------
# 必填：OpenAI 密钥
OPENAI_API_KEY=
# 必填：OpenAI API Base（官方为 https://api.openai.com/v1，或自有网关）
OPENAI_BASE_URL=
# 必填：默认模型（如 gpt-4o / gpt-4o-mini / o4-mini 等）
OPENAI_MODEL=gpt-4o-mini

# 可选：OpenAI 组织ID
OPENAI_ORG=
# 可选：OpenAI SDK 请求超时（毫秒）
OPENAI_TIMEOUT=20000

# 请求合并/缓存策略（布尔/数值，后端读取并决定是否启用）
LLM_ENABLE_CACHING=false
LLM_REQUEST_MERGE_WINDOW_MS=0

# ----------------------------
# 可观测性 / 错误上报
# ----------------------------
SENTRY_DSN=
# release 与 environment 由CI/CD在部署时注入更准确的值
SENTRY_RELEASE=local
SENTRY_ENVIRONMENT=development

# ----------------------------
# 安全与速率限制
# ----------------------------
# 后端 JWT 校验所需的受众/签发者（若使用 Supabase Auth，则可读取其JWKS进行校验）
JWT_AUDIENCE=
JWT_ISSUER=

# 基础速率限制（示例）
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=120

# ----------------------------
# 前端配置（可由构建注入）
# ----------------------------
VITE_API_BASE_URL=http://localhost:8000
VITE_SENTRY_DSN=
VITE_APP_ENV=development

# ----------------------------
# 流式与超时（后端外部调用基线）
# ----------------------------
# LLM 外部请求超时（毫秒）
LLM_REQUEST_TIMEOUT_MS=20000
# 外部请求最大重试次数
EXTERNAL_MAX_RETRIES=2
# 指数退避初始与最大间隔（毫秒）
RETRY_BACKOFF_INITIAL_MS=500
RETRY_BACKOFF_MAX_MS=8000

# ----------------------------
# 部署/CI（示例，按需在 GitHub Secrets 配置）
# ----------------------------
# CI 用于推送产物或部署平台API访问
CI_DEPLOY_TOKEN=
# Git SHA 或 版本号，由流水线注入
RELEASE_VERSION=

# ----------------------------
# 警告
# ----------------------------
# - 切勿将真实密钥提交到仓库
# - 本文件仅为示例，实际生产环境变量请放置在部署平台的安全存储中（GitHub Secrets / Render / Vercel 等）