# CI/CD 测试策略优化方案

作者：开发团队  
日期：2025-08-07  
版本：1.0  
关联文档：[测试策略指南](docs/testing-strategy-guide.md)、[架构文档](docs/architecture.md)

---

## 概述

基于 Story 1.13 的测试稳定性修复经验，设计优化的 CI/CD 测试执行策略，平衡测试覆盖率、执行效率和稳定性。

## 测试分组策略

### 1. 快速测试组 (Fast Tests)

**特征：**
- 执行时间 <5秒
- 使用 Fake Timers + 推进工具
- 纯单元测试，无外部依赖
- 高度并行化

**包含测试类型：**
- 组件渲染测试
- 工具函数测试
- 状态管理测试
- 简单交互测试

**CI 配置：**
```yaml
- name: Fast Tests
  run: pnpm test:fast -- --ci --reporter=default --maxWorkers=4
  timeout-minutes: 5
```

### 2. 稳定性测试组 (Stability Tests)

**特征：**
- 执行时间 5-15秒
- 使用真实时间 + DOM 条件等待
- 包含异步交互和 API 调用
- 适度并行化

**包含测试类型：**
- 长加载场景测试
- 复杂异步交互测试
- 端到端用户流程测试
- API 集成测试

**CI 配置：**
```yaml
- name: Stability Tests
  run: pnpm test:stability -- --ci --reporter=default --maxWorkers=2
  timeout-minutes: 15
```

### 3. 回归测试组 (Regression Tests)

**特征：**
- 执行时间可变
- 覆盖已知问题场景
- 包含边界条件测试
- 串行执行确保稳定性

**包含测试类型：**
- 已修复 bug 的回归测试
- 边界条件和错误场景测试
- 性能基线验证测试
- 跨浏览器兼容性测试

**CI 配置：**
```yaml
- name: Regression Tests
  run: pnpm test:regression -- --ci --reporter=default --maxWorkers=1
  timeout-minutes: 20
```

## 并行化执行方案

### 前端测试并行化

```yaml
strategy:
  fail-fast: false
  matrix:
    test-group: [fast, stability, regression]
    
steps:
  - name: Run ${{ matrix.test-group }} tests
    working-directory: apps/frontend
    run: pnpm test:${{ matrix.test-group }}
    timeout-minutes: ${{ matrix.test-group == 'fast' && 5 || matrix.test-group == 'stability' && 15 || 20 }}
```

### 后端测试并行化

```yaml
strategy:
  fail-fast: false
  matrix:
    test-type: [unit, integration, api]
    
steps:
  - name: Run ${{ matrix.test-type }} tests
    working-directory: apps/backend
    run: pytest tests/${{ matrix.test-type }}/ -v
```

## 性能监控指标

### 测试执行时间监控

```yaml
- name: Test Performance Monitoring
  run: |
    echo "::group::Test Execution Times"
    pnpm test:fast --reporter=verbose | tee fast-test-results.log
    pnpm test:stability --reporter=verbose | tee stability-test-results.log
    echo "::endgroup::"
    
    # 提取执行时间并设置告警阈值
    FAST_TIME=$(grep "Time:" fast-test-results.log | awk '{print $2}')
    STABILITY_TIME=$(grep "Time:" stability-test-results.log | awk '{print $2}')
    
    if (( $(echo "$FAST_TIME > 300" | bc -l) )); then
      echo "::warning::Fast tests exceeded 5 minutes: ${FAST_TIME}s"
    fi
    
    if (( $(echo "$STABILITY_TIME > 900" | bc -l) )); then
      echo "::warning::Stability tests exceeded 15 minutes: ${STABILITY_TIME}s"
    fi
```

### 测试稳定性监控

```yaml
- name: Test Stability Check
  run: |
    # 运行关键测试3次验证稳定性
    for i in {1..3}; do
      echo "::group::Stability Run $i"
      pnpm test:critical --reporter=default
      echo "::endgroup::"
    done
```

## 测试失败处理流程

### 1. 自动重试机制

```yaml
- name: Test with Retry
  uses: nick-invision/retry@v2
  with:
    timeout_minutes: 10
    max_attempts: 3
    retry_on: error
    command: pnpm test:stability
```

### 2. 失败分析和报告

```yaml
- name: Test Failure Analysis
  if: failure()
  run: |
    echo "::group::Test Failure Analysis"
    
    # 收集测试失败信息
    if [ -f test-results.xml ]; then
      echo "Test results found, analyzing failures..."
      # 解析测试结果并生成报告
    fi
    
    # 收集系统信息
    echo "Node version: $(node --version)"
    echo "NPM version: $(npm --version)"
    echo "System info: $(uname -a)"
    echo "Memory usage: $(free -h)"
    
    echo "::endgroup::"
```

### 3. 通知机制

```yaml
- name: Notify on Test Failure
  if: failure()
  uses: 8398a7/action-slack@v3
  with:
    status: failure
    text: "Tests failed in ${{ github.repository }} on ${{ github.ref }}"
  env:
    SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

## 环境特定配置

### CI 环境优化

```yaml
env:
  CI: true
  NODE_ENV: test
  # 增加 CI 环境的超时时间
  VITEST_TIMEOUT: 30000
  # 减少并行度以提高稳定性
  VITEST_MAX_WORKERS: 2
```

### 本地开发环境

```json
// package.json
{
  "scripts": {
    "test": "vitest",
    "test:fast": "vitest --testNamePattern='(unit|component)' --maxWorkers=4",
    "test:stability": "vitest --testNamePattern='(integration|e2e)' --maxWorkers=2",
    "test:regression": "vitest --testNamePattern='regression' --maxWorkers=1",
    "test:ci": "vitest --ci --reporter=default",
    "test:watch": "vitest --watch",
    "test:coverage": "vitest --coverage"
  }
}
```

## 缓存策略

### 依赖缓存

```yaml
- name: Cache node modules
  uses: actions/cache@v3
  with:
    path: |
      ~/.pnpm-store
      node_modules
    key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
    restore-keys: |
      ${{ runner.os }}-pnpm-
```

### 测试结果缓存

```yaml
- name: Cache test results
  uses: actions/cache@v3
  with:
    path: |
      coverage/
      test-results/
    key: ${{ runner.os }}-test-${{ github.sha }}
    restore-keys: |
      ${{ runner.os }}-test-
```

## 质量门禁

### 测试覆盖率要求

```yaml
- name: Coverage Check
  run: |
    pnpm test:coverage
    COVERAGE=$(grep -o '"pct":[0-9.]*' coverage/coverage-summary.json | head -1 | cut -d':' -f2)
    if (( $(echo "$COVERAGE < 80" | bc -l) )); then
      echo "::error::Coverage $COVERAGE% is below 80% threshold"
      exit 1
    fi
    echo "::notice::Coverage: $COVERAGE%"
```

### 测试稳定性要求

```yaml
- name: Stability Gate
  run: |
    # 要求关键测试连续3次通过
    for i in {1..3}; do
      pnpm test:critical || exit 1
    done
    echo "::notice::All stability tests passed"
```

## 监控和告警

### 性能回归检测

```yaml
- name: Performance Regression Check
  run: |
    # 与基线性能对比
    BASELINE_TIME=300  # 5分钟基线
    CURRENT_TIME=$(grep "Time:" test-results.log | awk '{print $2}')
    
    if (( $(echo "$CURRENT_TIME > $BASELINE_TIME * 1.2" | bc -l) )); then
      echo "::warning::Test execution time increased by >20%: ${CURRENT_TIME}s vs ${BASELINE_TIME}s baseline"
    fi
```

### 成功率监控

```yaml
- name: Success Rate Monitoring
  run: |
    # 计算测试成功率
    TOTAL_TESTS=$(grep "Tests:" test-results.log | awk '{print $2}')
    PASSED_TESTS=$(grep "Passed:" test-results.log | awk '{print $2}')
    SUCCESS_RATE=$(echo "scale=2; $PASSED_TESTS / $TOTAL_TESTS * 100" | bc)
    
    if (( $(echo "$SUCCESS_RATE < 95" | bc -l) )); then
      echo "::error::Test success rate $SUCCESS_RATE% is below 95% threshold"
      exit 1
    fi
    
    echo "::notice::Test success rate: $SUCCESS_RATE%"
```

## 实施计划

### Phase 1: 基础分组 (Week 1)
- [ ] 实施快速测试和稳定性测试分组
- [ ] 配置基本的并行化执行
- [ ] 设置性能监控基线

### Phase 2: 优化和监控 (Week 2)
- [ ] 实施缓存策略
- [ ] 添加失败处理和重试机制
- [ ] 配置告警和通知

### Phase 3: 持续改进 (Ongoing)
- [ ] 基于实际运行数据优化分组策略
- [ ] 调整并行度和超时时间
- [ ] 完善监控指标和告警规则

---

*本方案将根据实际运行效果持续优化和调整。*
