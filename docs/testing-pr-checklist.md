# 测试 PR 检查清单

## 概述

本检查清单用于确保所有包含测试代码的 Pull Request 符合项目的测试标准和最佳实践。

## 代码审查检查清单

### 📋 测试策略选择

- [ ] **测试策略选择合理**
  - [ ] 长时间等待测试 (>5s) 使用真实时间 + DOM 条件等待
  - [ ] 短时间精确控制 (<5s) 使用 Fake Timers + 推进工具
  - [ ] 复杂异步交互基于 DOM 状态变化
  - [ ] API 调用测试使用 Mock + 真实时间

- [ ] **异步处理正确**
  - [ ] 使用 `waitFor` 进行异步断言
  - [ ] 避免使用 `setTimeout` 等不确定的等待
  - [ ] 正确使用 `__advanceAndFlush__` 工具（仅在 fake timers 环境）
  - [ ] 微任务让步在合适的位置使用

### 🕒 超时和稳定性

- [ ] **超时时间合理**
  - [ ] 测试超时时间 ≤10秒
  - [ ] CI 环境考虑适当增加超时时间
  - [ ] 使用 `findBy*` 查询时设置合理的 timeout

- [ ] **测试稳定性**
  - [ ] 测试不依赖于具体的时间值
  - [ ] 避免竞态条件
  - [ ] 测试可以重复运行且结果一致

### 🧪 测试质量

- [ ] **测试命名清晰**
  - [ ] 使用描述性的测试名称
  - [ ] 遵循 "应该做什么" 的命名模式
  - [ ] 测试分组合理（describe 块）

- [ ] **断言有效**
  - [ ] 断言验证正确的行为
  - [ ] 避免过于宽泛的断言
  - [ ] 包含必要的错误场景测试

- [ ] **测试覆盖率**
  - [ ] 覆盖主要的功能路径
  - [ ] 包含边界条件测试
  - [ ] 包含错误处理测试

### 🔧 测试工具使用

- [ ] **Mock 使用正确**
  - [ ] Mock 对象行为符合真实实现
  - [ ] 在测试后正确清理 Mock
  - [ ] 避免过度 Mock

- [ ] **测试环境配置**
  - [ ] 正确使用 `beforeEach` 和 `afterEach`
  - [ ] 测试间相互独立
  - [ ] 正确清理定时器和事件监听器

### 🐛 调试友好性

- [ ] **错误信息清晰**
  - [ ] 测试失败时提供有用的错误信息
  - [ ] 使用 `screen.debug()` 等调试工具
  - [ ] 包含必要的上下文信息

- [ ] **调试工具使用**
  - [ ] 在复杂测试中添加调试信息输出
  - [ ] 使用测试状态快照（如需要）
  - [ ] 提供清晰的失败原因

## 性能检查

### ⚡ 执行效率

- [ ] **测试执行时间**
  - [ ] 单个测试文件执行时间合理
  - [ ] 避免不必要的真实网络请求
  - [ ] 合理使用并行执行

- [ ] **资源清理**
  - [ ] 及时清理定时器
  - [ ] 清理事件监听器
  - [ ] 清理 DOM 元素（如需要）

## CI/CD 兼容性

### 🚀 CI 环境

- [ ] **环境兼容性**
  - [ ] 测试在 CI 环境中稳定运行
  - [ ] 考虑 CI 环境的性能差异
  - [ ] 正确处理环境变量

- [ ] **并行执行**
  - [ ] 测试支持并行执行
  - [ ] 避免测试间的依赖关系
  - [ ] 正确处理共享资源

## 文档和注释

### 📚 文档完整性

- [ ] **测试文档**
  - [ ] 复杂测试逻辑有适当注释
  - [ ] 特殊测试策略有说明
  - [ ] 更新相关的测试文档

- [ ] **示例代码**
  - [ ] 提供的代码示例可以运行
  - [ ] 示例遵循最佳实践
  - [ ] 示例与实际使用场景一致

## 特定场景检查

### 🎯 React 组件测试

- [ ] **组件渲染**
  - [ ] 正确使用 `render` 函数
  - [ ] 提供必要的 props 和 context
  - [ ] 使用合适的查询方法

- [ ] **用户交互**
  - [ ] 使用 `fireEvent` 或 `userEvent` 模拟交互
  - [ ] 验证交互后的状态变化
  - [ ] 测试键盘和鼠标交互

### 🌐 API 测试

- [ ] **网络请求**
  - [ ] 正确 Mock API 调用
  - [ ] 测试成功和失败场景
  - [ ] 验证请求参数和响应处理

- [ ] **错误处理**
  - [ ] 测试网络错误场景
  - [ ] 验证错误信息显示
  - [ ] 测试重试机制

## 提交前检查

### ✅ 最终验证

- [ ] **本地测试通过**
  - [ ] 所有新增测试通过
  - [ ] 现有测试仍然通过
  - [ ] 测试覆盖率符合要求

- [ ] **代码质量**
  - [ ] 代码符合项目风格指南
  - [ ] 没有 console.log 等调试代码
  - [ ] 代码经过适当的重构

- [ ] **文档更新**
  - [ ] 更新相关的测试文档
  - [ ] 更新 README 中的测试说明（如需要）
  - [ ] 更新测试策略指南（如需要）

## 审查者检查清单

### 👥 代码审查

- [ ] **整体质量**
  - [ ] 测试逻辑清晰易懂
  - [ ] 测试覆盖了重要的功能点
  - [ ] 测试策略选择合理

- [ ] **最佳实践**
  - [ ] 遵循项目的测试标准
  - [ ] 使用推荐的测试模式
  - [ ] 避免常见的测试反模式

- [ ] **维护性**
  - [ ] 测试易于维护和更新
  - [ ] 测试不过于脆弱
  - [ ] 测试具有良好的可读性

## 参考资料

- [测试策略指南](docs/testing-strategy-guide.md)
- [架构文档 - 测试架构策略](docs/architecture.md#63-测试架构策略)
- [UI/UX 规范 - 测试清单](docs/ui-ux.md#11-测试清单testing-checklist)
- [Vitest 官方文档](https://vitest.dev/)
- [React Testing Library 最佳实践](https://testing-library.com/docs/react-testing-library/intro/)

---

*本检查清单将根据项目实践经验持续更新和完善。*
