# Reader.test.tsx 异步时序修复交接文档

## 🚨 问题概述

**症状**: Reader.test.tsx 的 2 个活跃 UI 测试用例在 Vitest+jsdom 环境下稳定性极差，超时失败率高（30s 超时）

**根因**: 多源异步链路（setTimeout/rAF）与 React/RTL 调度节拍不同步，导致"计时器推进但 DOM 未更新"的观测落空

## 📍 **重要发现**: 当前状态已实施部分修复

✅ **已完成的修复**:
- ✅ schedule 适配层已存在 (Reader.tsx:18-24)
- ✅ 微任务让步已添加到关键回调 (第248、352、378、420行)  
- ✅ 滚动恢复已简化 (rAF + timeout+微任务)
- ✅ 环境兼容性修复 (window/document 检测)

⚠️ **仍然存在的问题**: 
- 测试仍然超时（30s），说明已实施的修复不完全有效
- 需要进一步调试异步推进机制

## 🔍 深度分析 - 为什么现有修复无效

### 问题根源分析
1. **测试期望与实现不匹配**: 
   - 测试路径: `/reader/test-session` 但 Reader 组件期望 `:id` 参数
   - 组件中 `useParams<{ id: string }>()` 可能返回 undefined
   - 导致所有 `if (!id) return;` 的早期退出

2. **__advanceAndFlush__ 工具重复定义**:
   - setup.ts 中存在两处定义 (第85行和第124行)
   - 可能导致工具行为不一致

3. **fake timers 与 schedule 不协调**:
   - 测试使用 `vi.useFakeTimers()` 但 schedule 可能绕过了假时钟控制

## 🎯 新的修复策略

### P1: 修复测试路由问题（关键）
```typescript
// Reader.test.tsx 中的路由修正
// 原: initialEntries={["/reader/test-session"]}
// 改为: initialEntries={["/reader/s1"]} // 与 mock 的 session id 一致
```

### P2: 简化 __advanceAndFlush__ 定义
```typescript
// setup.ts 清理重复定义，保留最后一个异步版本
```

### P3: 强化 schedule 与 fake timers 协调
```typescript
// 确保 schedule 在测试环境下使用 vi.advanceTimersByTimeAsync 可控的 timer
```

### P2: 测试侧优化（并行进行）

#### 分阶段推进模式
```typescript
// 测试中的标准推进序列
test('async behavior', async () => {
  // 1. 执行操作
  await user.click(sendButton);
  
  // 2. 推进时钟
  await __advanceAndFlush__(10_000); // 或对应毫秒数
  
  // 3. 观测结果
  await waitFor(() => {
    expect(screen.getByText('预期内容')).toBeInTheDocument();
  }, { timeout: 3000, interval: 100 });
});
```

## 🔧 实施步骤

### Phase 1: 核心修复（30-60分钟）
1. 确认当前分支干净状态
2. 定位 Reader.tsx 中的四个异步点
3. 按上述模式逐一添加 `schedule.microtask(() => {})`
4. 运行测试验证: `npx vitest run src/pages/Reader.test.tsx --reporter=verbose`

### Phase 2: 测试稳定化（30分钟）
1. 优化测试中的推进序列
2. 统一 waitFor 超时配置
3. 多次运行确保稳定性

## ⚡ 快速验证清单

```bash
# 修复后验证命令
npx vitest run src/pages/Reader.test.tsx --reporter=verbose

# 预期结果: 1-3秒内全部通过
# ✅ Reader component › shows long loading indicator
# ✅ Reader component › handles auto retry
# ✅ Reader component › shows and hides summary
```

## 🚨 注意事项

### ❌ 避免的陷阱
- **禁止批量正则替换**: 只在确定的回调体末尾定点添加
- **避免破坏语法**: 确保 async 函数正确标记
- **不要过度工程化**: 保持最小变更原则

### ✅ 验证要点
- 微任务让步不改变业务语义
- schedule 适配层保持向后兼容
- 滚动恢复功能完整性

## 🔍 故障排除

**如果修复后仍偶现不稳**:
1. 在对应分支回调末尾再加一层 `setTimeout(0)` 兜底
2. 检查是否有遗漏的异步回调未处理
3. 验证测试推进序列是否与业务时序匹配

## 📊 成功标准

- [ ] 3个测试用例连续运行5次均通过
- [ ] 单次测试运行时间 < 5秒
- [ ] 业务功能无回归
- [ ] 代码变更 < 20行

## 🔄 后续优化方向

1. **架构性增强**: 统一所有时钟行为经过 schedule
2. **测试基础设施**: 完善 __advanceAndFlush__ 工具链
3. **监控告警**: 添加异步时序监控机制

---
**联系信息**: 如遇阻塞或需澄清，随时沟通交流
**预估工时**: 1-2小时完成核心修复
**风险等级**: 低（变更面小，可快速回滚）