# Zhiread Backend Makefile

# Python version and virtual environment
PYTHON := python3.11
VENV_DIR := .venv
VENV_BIN := $(VENV_DIR)/bin
BACKEND_DIR := apps/backend

# Environment and configuration
ENV_FILE := $(BACKEND_DIR)/.env
ENV_EXAMPLE := $(BACKEND_DIR)/.env.example
# Root env (single source of truth)
ROOT_ENV := .env

.PHONY: help venv install install-dev dev migrate migrate-create migrate-upgrade migrate-downgrade test lint clean docker-build backend-env-sync backend-up

# Default target
help: ## Show this help message
	@echo "Zhiread Backend Development Commands"
	@echo "===================================="
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Virtual environment setup
venv: ## Create Python virtual environment
	$(PYTHON) -m venv $(VENV_DIR)
	@echo "Virtual environment created at $(VENV_DIR)"
	@echo "Activate with: source $(VENV_BIN)/activate"

install: venv ## Install production dependencies
	$(VENV_BIN)/pip install --upgrade pip
	$(VENV_BIN)/pip install -r $(BACKEND_DIR)/requirements.txt

install-dev: install ## Install development dependencies  
	$(VENV_BIN)/pip install pytest pytest-asyncio httpx black ruff

# Sync backend .env from root .env (Single Source of Truth)
backend-env-sync: ## Generate apps/backend/.env from root .env (OPENAI_*, LLM_*, DB, API settings)
	@if [ ! -f $(ROOT_ENV) ]; then \
		echo "Root $(ROOT_ENV) not found. Please create it from .env.example"; \
		exit 1; \
	fi
	@echo "# Auto-generated from root .env - do not commit secrets" > $(ENV_FILE)
	@echo "# Generated at: $$(date -u +"%Y-%m-%dT%H:%M:%SZ")" >> $(ENV_FILE)
	@echo "" >> $(ENV_FILE)
	@awk -F= '/^(OPENAI_|LLM_|DATABASE_URL|API_PREFIX|APP_HOST|APP_PORT|APP_DEBUG|ENVIRONMENT|SENTRY_DSN|SUPABASE_URL|SUPABASE_SERVICE_ROLE_KEY|SUPABASE_ANON_KEY|RETRY_BACKOFF_|EXTERNAL_MAX_RETRIES)/ && $$1 !~ /^#/ {print}' $(ROOT_ENV) >> $(ENV_FILE)
	@echo "✓ Synced $(ENV_FILE) from $(ROOT_ENV)"

# Development server
dev: ## Run development server with auto-reload
	@if [ ! -f $(ENV_FILE) ]; then \
		echo "apps/backend/.env not found. Running backend-env-sync..."; \
		$(MAKE) backend-env-sync; \
	fi
	cd $(BACKEND_DIR) && $(VENV_BIN)/uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# One-step: install deps, sync env, run dev
backend-up: install backend-env-sync ## Install deps, sync env from root, and start dev server
	cd $(BACKEND_DIR) && $(VENV_BIN)/uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Database migrations
migrate-create: ## Create new migration (MESSAGE required)
	@if [ -z "$(MESSAGE)" ]; then \
		echo "Usage: make migrate-create MESSAGE='description'"; \
		exit 1; \
	fi
	cd $(BACKEND_DIR) && $(VENV_BIN)/alembic revision --autogenerate -m "$(MESSAGE)"

migrate-upgrade: ## Apply all pending migrations
	cd $(BACKEND_DIR) && $(VENV_BIN)/alembic upgrade head

migrate-downgrade: ## Rollback one migration
	cd $(BACKEND_DIR) && $(VENV_BIN)/alembic downgrade -1

migrate-status: ## Show current migration status
	cd $(BACKEND_DIR) && $(VENV_BIN)/alembic current

migrate-history: ## Show migration history
	cd $(BACKEND_DIR) && $(VENV_BIN)/alembic history

# Testing
test: ## Run all tests
	cd $(BACKEND_DIR) && $(VENV_BIN)/pytest tests/ -v

test-coverage: ## Run tests with coverage report
	cd $(BACKEND_DIR) && $(VENV_BIN)/pytest tests/ --cov=app --cov-report=html --cov-report=term

# Code quality
lint: ## Run linting and formatting
	cd $(BACKEND_DIR) && $(VENV_BIN)/ruff check app/ tests/
	cd $(BACKEND_DIR) && $(VENV_BIN)/black --check app/ tests/

format: ## Format code
	cd $(BACKEND_DIR) && $(VENV_BIN)/black app/ tests/
	cd $(BACKEND_DIR) && $(VENV_BIN)/ruff check --fix app/ tests/

# Database utilities
db-reset: ## Reset database (WARNING: destroys all data)
	cd $(BACKEND_DIR) && $(VENV_BIN)/alembic downgrade base
	cd $(BACKEND_DIR) && $(VENV_BIN)/alembic upgrade head

# Cleanup
clean: ## Clean up temporary files
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete
	find . -type f -name ".coverage" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name "htmlcov" -exec rm -rf {} +

# Docker (future use)
docker-build: ## Build Docker image
	docker build -t zhiread-backend -f $(BACKEND_DIR)/Dockerfile .

docker-run: ## Run Docker container
	docker run -p 8000:8000 --env-file $(ENV_FILE) zhiread-backend

# Quick start
quickstart: install migrate-upgrade ## Quick setup for new development environment
	@echo "Quick start complete!"
	@echo "1. Edit $(ENV_FILE) with your database configuration"
	@echo "2. Run 'make dev' to start the development server"

# Health check
health: ## Check if all services are working
	@echo "Checking Python version..."
	$(PYTHON) --version
	@echo "Checking virtual environment..."
	@test -f $(VENV_BIN)/python && echo "✓ Virtual environment exists" || echo "✗ Virtual environment missing"
	@echo "Checking dependencies..."
	@test -f $(BACKEND_DIR)/requirements.txt && echo "✓ Requirements file exists" || echo "✗ Requirements file missing"
	@echo "Checking configuration..."
	@test -f $(ENV_EXAMPLE) && echo "✓ Environment example exists" || echo "✗ Environment example missing"