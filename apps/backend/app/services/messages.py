"""
Message Service

Handles message creation, AI chat completion, and summary generation.
"""

import logging
from typing import List, Optional
from uuid import UUID, uuid4
from datetime import datetime, timezone

from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app.core.errors import AppError, ErrorCode
from app.models.schemas import (
    MessageResponse, 
    ChatCompletionResponse, 
    Summary
)
from app.services.llm import llm_service, LLMMessage
from app.services.sessions import session_service


logger = logging.getLogger(__name__)


class MessageService:
    """Service for handling messages and AI interactions."""
    
    async def send_message(
        self,
        db: Session,
        session_id: UUID,
        user_id: UUID,
        content: str
    ) -> ChatCompletionResponse:
        """
        Send a user message and get AI response.
        
        Args:
            db: Database session
            session_id: Session ID
            user_id: User ID
            content: User message content
            
        Returns:
            ChatCompletionResponse with AI message and optional updated summary
        """
        # Verify session exists and user has access
        session_detail = session_service.get_session_by_id(
            db=db, session_id=session_id, user_id=user_id
        )
        if not session_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会话不存在或无权限访问。"
            )
        
        try:
            # Create user message
            user_message = self._create_message(
                db=db,
                session_id=session_id,
                role="user",
                content=content
            )
            
            # Prepare conversation history for LLM
            conversation_history = self._build_conversation_history(
                session_detail=session_detail,
                new_user_message=user_message
            )
            
            # Get AI response
            ai_response = await self._get_ai_response(conversation_history)
            
            # Create assistant message
            assistant_message = self._create_message(
                db=db,
                session_id=session_id,
                role="assistant",
                content=ai_response.content
            )
            
            # Generate or update summary if needed
            updated_summary = await self._update_summary_if_needed(
                db=db,
                session_id=session_id,
                conversation_history=conversation_history,
                new_ai_response=ai_response.content
            )
            
            return ChatCompletionResponse(
                message=MessageResponse(
                    id=assistant_message.id,
                    role=assistant_message.role,
                    content=assistant_message.content,
                    created_at=assistant_message.created_at
                ),
                summary=updated_summary
            )
            
        except AppError:
            raise
        except Exception as e:
            logger.error(f"Error in send_message: {str(e)}")
            raise AppError(
                error_code=ErrorCode.INTERNAL_ERROR,
                message="处理消息时发生错误",
                details={"error": str(e)}
            )
    
    def _create_message(
        self,
        db: Session,
        session_id: UUID,
        role: str,
        content: str
    ) -> MessageResponse:
        """Create a message record in the database."""
        # For now, we'll create a mock message since we don't have the database model yet
        # TODO: Implement actual database storage
        message_id = uuid4()
        created_at = datetime.now(timezone.utc)
        
        # Mock message creation - in real implementation, this would save to database
        logger.info(f"Created {role} message for session {session_id}: {content[:100]}...")
        
        return MessageResponse(
            id=message_id,
            role=role,
            content=content,
            created_at=created_at
        )
    
    def _build_conversation_history(
        self,
        session_detail,
        new_user_message: MessageResponse
    ) -> List[LLMMessage]:
        """Build conversation history for LLM context."""
        messages = []
        
        # Add system message with session context
        system_prompt = self._build_system_prompt(session_detail)
        messages.append(LLMMessage(role="system", content=system_prompt))
        
        # Add existing conversation history
        for msg in session_detail.messages:
            messages.append(LLMMessage(role=msg.role, content=msg.content))
        
        # Add new user message
        messages.append(LLMMessage(role=new_user_message.role, content=new_user_message.content))
        
        return messages
    
    def _build_system_prompt(self, session_detail) -> str:
        """Build system prompt with session context."""
        content_text = "\n".join([p.text for p in session_detail.content.paragraphs])
        
        system_prompt = f"""你是一个智能学习助手，帮助用户深度理解和学习文本内容。

当前学习材料：
标题：{session_detail.title}
内容：
{content_text[:2000]}{"..." if len(content_text) > 2000 else ""}

你的任务：
1. 基于用户的问题，提供准确、有帮助的回答
2. 引导用户深入思考和理解内容
3. 提供相关的背景知识和解释
4. 保持回答简洁明了，避免冗长

请用中文回答用户的问题。"""
        
        return system_prompt
    
    async def _get_ai_response(self, messages: List[LLMMessage]):
        """Get AI response from LLM service."""
        try:
            response = await llm_service.chat_completion(
                messages=messages,
                stream=False
            )
            return response
        except AppError:
            raise
        except Exception as e:
            logger.error(f"LLM service error: {str(e)}")
            raise AppError(
                error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
                message="AI 服务暂时不可用，请稍后重试",
                details={"error": str(e)}
            )
    
    async def _update_summary_if_needed(
        self,
        db: Session,
        session_id: UUID,
        conversation_history: List[LLMMessage],
        new_ai_response: str
    ) -> Optional[Summary]:
        """Update summary if conversation has grown significantly."""
        # Simple heuristic: update summary every 6 messages
        message_count = len([msg for msg in conversation_history if msg.role in ["user", "assistant"]])
        
        if message_count >= 6 and message_count % 6 == 0:
            return await self._generate_summary(
                db=db,
                session_id=session_id,
                conversation_history=conversation_history
            )
        
        return None
    
    async def _generate_summary(
        self,
        db: Session,
        session_id: UUID,
        conversation_history: List[LLMMessage]
    ) -> Summary:
        """Generate a new summary of the conversation."""
        try:
            # Build summary prompt
            conversation_text = "\n".join([
                f"{msg.role}: {msg.content}" 
                for msg in conversation_history 
                if msg.role in ["user", "assistant"]
            ])
            
            summary_messages = [
                LLMMessage(
                    role="system",
                    content="请为以下对话生成一个简洁的摘要，突出关键讨论点和学习要点。用中文回答，控制在200字以内。"
                ),
                LLMMessage(
                    role="user",
                    content=f"对话内容：\n{conversation_text}"
                )
            ]
            
            response = await llm_service.chat_completion(
                messages=summary_messages,
                stream=False
            )
            
            # Create summary record
            # TODO: Implement actual database storage
            summary_id = uuid4()
            created_at = datetime.now(timezone.utc)
            
            logger.info(f"Generated summary for session {session_id}: {response.content[:100]}...")
            
            return Summary(
                id=summary_id,
                session_id=session_id,
                version=1,  # TODO: Implement proper versioning
                text=response.content,
                created_at=created_at
            )
            
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            # Don't fail the whole request if summary generation fails
            return None


# Global message service instance
message_service = MessageService()
