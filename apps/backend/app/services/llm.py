"""
LLM Service Adapter

Provides a unified interface for different LLM providers (OpenAI, Claude, Gemini, etc.)
with streaming support, error handling, and retry mechanisms.
"""

import asyncio
import json
import logging
from typing import AsyncGenerator, Dict, List, Optional, Any, Tuple, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
import time

import httpx
from app.core.config import settings
from app.core.errors import AppError, ErrorCode


logger = logging.getLogger(__name__)


class LLMProvider(str, Enum):
    """Supported LLM providers."""
    OPENAI = "openai"
    CLAUDE = "claude"
    GEMINI = "gemini"
    OPENAI_SDK = "openai-sdk"


@dataclass
class LLMMessage:
    """Standardized message format for LLM interactions."""
    role: str  # "system", "user", "assistant"
    content: str
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LLMResponse:
    """Standardized response format from LLM."""
    content: str
    model: str
    usage: Optional[Dict[str, int]] = None
    metadata: Optional[Dict[str, Any]] = None  # e.g. provider, latency_ms, attempts, retry_policy, http_status, streaming


class LLMAdapter(ABC):
    """Abstract base class for LLM adapters."""
    
    def __init__(self, api_key: str, api_base: Optional[str] = None):
        self.api_key = api_key
        self.api_base = api_base
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(settings.llm_request_timeout_ms / 1000.0)
        )
    
    @abstractmethod
    async def chat_completion(
        self,
        messages: List[LLMMessage],
        model: Optional[str] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[AsyncGenerator[str, None], LLMResponse]:
        """Generate chat completion."""
        pass
    
    @abstractmethod
    def _format_messages(self, messages: List[LLMMessage]) -> List[Dict[str, Any]]:
        """Format messages for the specific provider."""
        pass
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


class OpenAIAdapter(LLMAdapter):
    """OpenAI REST API adapter (legacy)."""
    
    def __init__(self, api_key: str, api_base: Optional[str] = None):
        super().__init__(api_key, api_base)
        self.api_base = api_base or "https://api.openai.com/v1"
    
    def _format_messages(self, messages: List[LLMMessage]) -> List[Dict[str, Any]]:
        """Format messages for OpenAI API."""
        return [{"role": msg.role, "content": msg.content} for msg in messages]
    
    async def chat_completion(
        self,
        messages: List[LLMMessage],
        model: Optional[str] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[AsyncGenerator[str, None], LLMResponse]:
        """Generate chat completion using OpenAI REST API."""
        # Note: legacy adapter not used anymore; keep compatibility if ever invoked
        model = model or settings.openai_model
        formatted_messages = self._format_messages(messages)
        
        payload = {
            "model": model,
            "messages": formatted_messages,
            "stream": stream,
            **kwargs
        }
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        if stream:
            return self._stream_completion(payload, headers)
        else:
            return await self._single_completion(payload, headers, model)
    
    async def _single_completion(
        self, payload: Dict, headers: Dict, model: str
    ) -> LLMResponse:
        """Handle single (non-streaming) completion."""
        start = time.perf_counter()
        try:
            response = await self.client.post(
                f"{self.api_base}/chat/completions",
                json=payload,
                headers=headers
            )
            http_status = response.status_code
            response.raise_for_status()
            
            data = response.json()
            content = data["choices"][0]["message"]["content"]
            usage = data.get("usage", {})
            
            latency_ms = int((time.perf_counter() - start) * 1000)
            return LLMResponse(
                content=content,
                model=model,
                usage=usage,
                metadata={
                    "provider": "openai",
                    "model": model,
                    "latency_ms": latency_ms,
                    "attempts": 1,
                    "retry_policy": {
                        "max_retries": settings.external_max_retries,
                        "initial_ms": settings.retry_backoff_initial_ms,
                        "max_ms": settings.retry_backoff_max_ms,
                    },
                    "error_type": None,
                    "http_status": http_status,
                    "streaming": False,
                },
            )
            
        except httpx.HTTPStatusError as e:
            http_status = e.response.status_code if e.response else None
            logger.error(f"LLM API error: {http_status} - {getattr(e.response, 'text', '')}")
            raise AppError(
                error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
                message=f"LLM API error: {http_status}",
                details={"provider": "openai", "status_code": http_status}
            )
        except Exception as e:
            logger.error(f"Unexpected LLM error: {str(e)}")
            raise AppError(
                error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
                message="LLM service unavailable",
                details={"provider": "openai", "error": str(e)}
            )
    
    async def _stream_completion(
        self, payload: Dict, headers: Dict
    ) -> AsyncGenerator[str, None]:
        """Handle streaming completion."""
        start = time.perf_counter()
        http_status_holder: Dict[str, Optional[int]] = {"status": None}
        try:
            async with self.client.stream(
                "POST",
                f"{self.api_base}/chat/completions",
                json=payload,
                headers=headers
            ) as response:
                http_status_holder["status"] = response.status_code
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]  # Remove "data: " prefix
                        
                        if data_str.strip() == "[DONE]":
                            break
                        
                        try:
                            data = json.loads(data_str)
                            if "choices" in data and len(data["choices"]) > 0:
                                delta = data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    yield delta["content"]
                        except json.JSONDecodeError:
                            continue
                # end stream
                latency_ms = int((time.perf_counter() - start) * 1000)
                logger.info(
                    f"LLM streaming completed | provider=openai model={payload.get('model')} "
                    f"latency_ms={latency_ms} http_status={http_status_holder['status']}"
                )
                            
        except httpx.HTTPStatusError as e:
            logger.error(f"LLM streaming error: {e.response.status_code}")
            raise AppError(
                error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
                message=f"LLM streaming error: {e.response.status_code}",
                details={"provider": "openai", "status_code": e.response.status_code}
            )


class OpenAILibraryAdapter(LLMAdapter):
    """OpenAI official SDK adapter using AsyncOpenAI."""
    def __init__(self, api_key: str, api_base: Optional[str] = None, org: Optional[str] = None, timeout_ms: Optional[int] = None):
        # Do not create httpx client; SDK manages its own client and timeouts
        self.api_key = api_key
        self.api_base = api_base
        self.org = org
        self.timeout_ms = timeout_ms or settings.openai_timeout_ms
        # lazy import to avoid hard dependency during startup
        from openai import AsyncOpenAI  # type: ignore
        self._client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.api_base,
            organization=self.org,
            timeout=self.timeout_ms / 1000.0 if self.timeout_ms else None,
        )

    def _format_messages(self, messages: List[LLMMessage]) -> List[Dict[str, Any]]:
        return [{"role": msg.role, "content": msg.content} for msg in messages]

    async def chat_completion(
        self,
        messages: List[LLMMessage],
        model: Optional[str] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[AsyncGenerator[str, None], LLMResponse]:
        """Generate chat completion compatible with LLMService interface."""
        model = model or settings.openai_model
        formatted_messages = self._format_messages(messages)
        try:
            if stream:
                return self._stream_completion(formatted_messages, model, **kwargs)
            else:
                resp = await self._client.chat.completions.create(  # openai>=1.0
                    model=model,
                    messages=formatted_messages,
                    stream=False,
                    **kwargs
                )
                content = resp.choices[0].message.content or ""
                usage = None
                if getattr(resp, "usage", None):
                    usage = {
                        "prompt_tokens": resp.usage.prompt_tokens,
                        "completion_tokens": resp.usage.completion_tokens,
                        "total_tokens": resp.usage.total_tokens,
                    }
                return LLMResponse(content=content, model=model, usage=usage)
        except Exception as e:
            # Map to AppError for unified handling
            logger.error(f"OpenAI SDK error: {e}")
            raise AppError(
                error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
                message="OpenAI SDK error",
                details={"provider": "openai-sdk", "error": str(e)}
            )

    async def _stream_completion(
        self, formatted_messages: List[Dict[str, Any]], model: str, **kwargs
    ) -> AsyncGenerator[str, None]:
        try:
            stream_resp = await self._client.chat.completions.create(
                model=model,
                messages=formatted_messages,
                stream=True,
                **kwargs
            )
            async def generator():
                async for chunk in stream_resp:
                    for choice in getattr(chunk, "choices", []):
                        delta = getattr(choice, "delta", None)
                        if delta and getattr(delta, "content", None):
                            yield delta.content
            return generator()
        except Exception as e:
            logger.error(f"OpenAI SDK streaming error: {e}")
            raise AppError(
                error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
                message="OpenAI SDK streaming error",
                details={"provider": "openai-sdk", "error": str(e)}
            )

    async def close(self):
        try:
            await self._client.close()
        except Exception:
            pass


class LLMService:
    """Main LLM service with provider abstraction and retry logic."""
    
    def __init__(self):
        self.adapter: Optional[LLMAdapter] = None
        self._initialize_adapter()
    
    def _initialize_adapter(self):
        """Initialize the LLM adapter. Force usage of OpenAI official SDK as per migration policy."""
        # Enforce OpenAI official SDK usage (no local REST fallback)
        provider = (getattr(settings, "llm_provider", "openai-sdk") or "openai-sdk").lower()

        # Resolve credentials strictly from OpenAI-first variables, fallback to generic for compatibility
        api_key = getattr(settings, "openai_api_key", None) or settings.llm_api_key
        api_base = getattr(settings, "openai_base_url", None) or settings.llm_api_base
        org = getattr(settings, "openai_org", None)
        timeout_ms = getattr(settings, "openai_timeout_ms", None) or settings.llm_request_timeout_ms

        if not api_key:
            logger.warning("No OpenAI API key configured")
            return

        try:
            self.adapter = OpenAILibraryAdapter(
                api_key=api_key,
                api_base=api_base,
                org=org,
                timeout_ms=timeout_ms,
            )
            logger.info("LLM service initialized with OpenAI official SDK adapter (forced)")
        except Exception as e:
            # As per requirement: completely deprecate local LLM/REST path; do not fallback.
            logger.error(f"Failed to init OpenAI SDK adapter: {e}")
            raise AppError(
                error_code=ErrorCode.SERVICE_UNAVAILABLE,
                message="Failed to initialize OpenAI SDK adapter",
                details={"provider": "openai-sdk", "error": str(e)}
            )
    
    async def chat_completion(
        self,
        messages: List[LLMMessage],
        model: Optional[str] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[AsyncGenerator[str, None], LLMResponse]:
        """Generate chat completion with retry logic and observability metadata."""
        if not self.adapter:
            raise AppError(
                error_code=ErrorCode.SERVICE_UNAVAILABLE,
                message="LLM service not configured",
                details={"reason": "missing_api_key"}
            )
        
        attempts = 0
        start_all = time.perf_counter()
        for attempt in range(settings.external_max_retries + 1):
            attempts = attempt + 1
            try:
                result = await self.adapter.chat_completion(
                    messages=messages,
                    model=model,
                    stream=stream,
                    **kwargs
                )
                # For non-streaming responses, enrich metadata if adapter did not provide
                if not stream and isinstance(result, LLMResponse):
                    if result.metadata is None:
                        result.metadata = {}
                    result.metadata.setdefault("provider", "openai-sdk" if isinstance(self.adapter, OpenAILibraryAdapter) else "openai")
                    result.metadata.setdefault("model", model or settings.openai_model)
                    result.metadata.setdefault("attempts", attempts)
                    result.metadata.setdefault("retry_policy", {
                        "max_retries": settings.external_max_retries,
                        "initial_ms": settings.retry_backoff_initial_ms,
                        "max_ms": settings.retry_backoff_max_ms,
                    })
                    result.metadata.setdefault("streaming", False)
                    result.metadata.setdefault("latency_ms", int((time.perf_counter() - start_all) * 1000))
                return result
            except AppError as e:
                if attempt == settings.external_max_retries:
                    raise
                
                # Calculate exponential backoff delay
                delay = min(
                    settings.retry_backoff_initial_ms * (2 ** attempt) / 1000.0,
                    settings.retry_backoff_max_ms / 1000.0
                )
                
                logger.warning(
                    f"LLM request failed (attempt {attempt + 1}), retrying in {delay}s: {e.message}"
                )
                await asyncio.sleep(delay)
    
    async def close(self):
        """Close the LLM service and cleanup resources."""
        if self.adapter:
            await self.adapter.close()


# Global LLM service instance
llm_service = LLMService()
