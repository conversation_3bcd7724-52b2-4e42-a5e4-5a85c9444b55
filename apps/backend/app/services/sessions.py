from typing import List, Optional, Union
from uuid import UUID
from sqlalchemy.orm import Session

from app.crud.sessions import session_crud
from app.models.schemas import (
    SessionCreate,
    SessionResponse,
    SessionListResponse,
    SessionDetailResponse,
    SessionContent,
    SessionParagraph,
    SessionProgressResponse,
    Message,
    Summary,
)
from app.models.session import Session as SessionModel
from fastapi import HTTPException, status


class SessionService:
    """Business logic service for session operations."""
    
    def create_session(
        self,
        db: Session,
        session_data: SessionCreate,
        user_id: UUID
    ) -> SessionResponse:
        """Create a new session and return response model."""
        db_session = session_crud.create_session(
            db=db,
            session_data=session_data,
            user_id=user_id
        )
        return SessionResponse.model_validate(db_session)
    
    def get_user_sessions(
        self,
        db: Session,
        user_id: UUID,
        skip: int = 0,
        limit: int = 100
    ) -> SessionListResponse:
        """Get user sessions with pagination."""
        MAX_LIMIT = 50
        safe_limit = min(max(limit, 0), MAX_LIMIT)
        sessions = session_crud.get_sessions_by_user(
            db=db,
            user_id=user_id,
            skip=skip,
            limit=safe_limit
        )
        total = session_crud.count_sessions_by_user(db=db, user_id=user_id)
        session_responses = [SessionResponse.model_validate(session) for session in sessions]
        return SessionListResponse(items=session_responses, total=total)
    
    def get_session_by_id(
        self,
        db: Session,
        session_id: UUID,
        user_id: UUID
    ) -> Union[SessionDetailResponse, None]:
        """
        Get a specific session by ID for the user.

        返回内容包含：
        - content（用于 Reader 的最小渲染内容）
        - messages（消息历史，升序）
        - summary_latest（最新摘要，或 None）
        - reading_position（百分比 0-100，或 None）
        """
        db_session = session_crud.get_session_by_id(
            db=db,
            session_id=session_id,
            user_id=user_id
        )
        if not db_session:
            return None

        # 内容（暂以示例填充，后续可切换为真实数据源）
        sample_content = SessionContent(
            language="en",
            source="sample",
            paragraphs=[
                SessionParagraph(index=0, text="This is a sample article used for Reader rendering."),
                SessionParagraph(index=1, text="It demonstrates how paragraphs are delivered by the API."),
                SessionParagraph(index=2, text="Replace this with real content data source in future iterations."),
            ],
        )

        # 消息历史：由 CRUD 层按 created_at 升序返回领域对象，这里转为 Pydantic
        db_messages = session_crud.get_messages_by_session(db=db, session_id=session_id, user_id=user_id)
        messages: List[Message] = [
            Message(
                id=m.id,
                role=m.role,
                content=m.content,
                created_at=m.created_at,
            )
            for m in (db_messages or [])
        ]

        # 摘要：从 summaries 中取 version 最大的一条
        db_summaries = session_crud.get_summaries_by_session(db=db, session_id=session_id, user_id=user_id)
        latest_summary: Optional[Summary] = None
        if db_summaries:
            s = max(db_summaries, key=lambda x: getattr(x, "version", 0))
            latest_summary = Summary(
                id=s.id,
                session_id=s.session_id,
                version=s.version,
                text=s.text,
                created_at=s.created_at,
            )

        # 阅读位置：沿用进度百分比
        progress_percent = None
        try:
            progress_percent = session_crud.read_progress_percent(db_session)
            if progress_percent is not None:
                progress_percent = max(0, min(100, int(progress_percent)))
        except Exception:
            progress_percent = None

        detail = SessionDetailResponse(
            id=db_session.id,
            title=db_session.title,
            created_at=db_session.created_at,
            content=sample_content,
            messages=messages,
            summary_latest=latest_summary,
            reading_position=progress_percent,
        )
        return detail

    # --- 阅读进度（含 ETag/If-Match 语义支持在路由层设置头/读取头） ---
    def get_session_progress(
        self,
        db: Session,
        session_id: UUID,
        user_id: UUID
    ) -> SessionProgressResponse:
        """Read progressPercent and let router attach ETag via progressVersion."""
        db_session = session_crud.get_session_by_id(db=db, session_id=session_id, user_id=user_id)
        if not db_session:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="会话不存在或无权限访问。")
        progress = session_crud.read_progress_percent(db_session)
        # clamp
        progress = max(0, min(100, int(progress)))
        return SessionProgressResponse(session_id=db_session.id, progress=progress)

    def get_progress_version_meta(self, db: Session, session_id: UUID, user_id: UUID) -> dict:
        """Return metadata for current server progress for ETag and conflict responses."""
        db_session = session_crud.get_session_by_id(db=db, session_id=session_id, user_id=user_id)
        if not db_session:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="会话不存在或无权限访问。")
        return session_crud.read_progress_meta(db_session)

    def update_session_progress(
        self,
        db: Session,
        session_id: UUID,
        user_id: UUID,
        progress: int,
        *,
        if_match: Union[str, None],
        device_id: Union[str, None],
        user_agent: Union[str, None],
    ) -> tuple[SessionProgressResponse, str]:
        """
        Update progress with optimistic concurrency using ETag (If-Match).
        Returns (response_model, new_version) on success.
        Raises HTTPException 428 if If-Match missing, 409 if version mismatch.
        """
        # Validate input progress
        if not isinstance(progress, int) or progress < 0 or progress > 100:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="progress must be 0-100 integer")

        db_session = session_crud.get_session_by_id(db=db, session_id=session_id, user_id=user_id)
        if not db_session:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="会话不存在或无权限访问。")

        # Require If-Match
        if not if_match:
            # 428 Precondition Required
            raise HTTPException(status_code=428, detail="If-Match header required for concurrency control")

        current_version = session_crud.read_progress_version(db_session)
        if current_version and if_match != current_version:
            # Version conflict -> 409 with server state details
            meta = session_crud.read_progress_meta(db_session)
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail={
                    "error": "conflict",
                    "server": {
                        "offset": meta.get("progress"),  # remains percent in current implementation
                        "updatedAt": meta.get("updatedAt"),
                        "version": meta.get("version"),
                        "lastSource": meta.get("lastSource") or {},
                    },
                },
            )

        # Write new progress and bump version/meta
        db_session = session_crud.write_progress_with_meta(
            db=db,
            db_session=db_session,
            progress=int(progress),
            device_id=device_id,
            user_agent=user_agent,
        )
        meta = session_crud.read_progress_meta(db_session)
        new_version = meta.get("version") or ""
        resp = SessionProgressResponse(session_id=db_session.id, progress=int(progress))
        return resp, new_version


# Create singleton instance
session_service = SessionService()