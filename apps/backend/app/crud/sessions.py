from typing import List, Optional, Union, Union
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import desc

from app.models.session import Session as SessionModel
from app.models.schemas import SessionCreate
import json


class SessionCRUD:
    """CRUD operations for Session model."""
    
    def create_session(
        self,
        db: Session,
        session_data: SessionCreate,
        user_id: UUID
    ) -> SessionModel:
        """Create a new session."""
        title = self._generate_title(session_data.text)
        db_session = SessionModel(
            user_id=user_id,
            title=title,
            reading_state={}  # Initialize empty reading state
        )
        db.add(db_session)
        db.commit()
        db.refresh(db_session)
        return db_session
    
    def get_sessions_by_user(
        self,
        db: Session,
        user_id: UUID,
        skip: int = 0,
        limit: int = 100
    ) -> List[SessionModel]:
        """Get sessions for a specific user, ordered by creation date (newest first)."""
        return (
            db.query(SessionModel)
            .filter(SessionModel.user_id == user_id)
            .order_by(desc(SessionModel.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_session_by_id(
        self,
        db: Session,
        session_id: UUID,
        user_id: Optional[UUID] = None
    ) -> Optional[SessionModel]:
        """Get a specific session by ID, optionally filtered by user."""
        query = db.query(SessionModel).filter(SessionModel.id == session_id)
        if user_id:
            query = query.filter(SessionModel.user_id == user_id)
        return query.first()
    
    def count_sessions_by_user(self, db: Session, user_id: UUID) -> int:
        """Count total sessions for a user."""
        return db.query(SessionModel).filter(SessionModel.user_id == user_id).count()

    # --- 新增：消息与摘要查询（按架构契约） ---
    def get_messages_by_session(self, db: Session, session_id: UUID, user_id: Optional[UUID] = None):
        """
        返回该会话的消息列表，按 created_at 升序。
        说明：当前项目未定义 Message/Summary ORM 表，先返回空列表以保持接口幂等。
        后续若添加 ORM：在此处 join/过滤并排序。
        """
        try:
            # TODO: replace with real ORM query when Message model exists
            return []
        except Exception:
            return []

    def get_summaries_by_session(self, db: Session, session_id: UUID, user_id: Optional[UUID] = None):
        """
        返回该会话的摘要列表（用于选取最新版本）。
        说明：当前项目未定义 Summary ORM 表，先返回空列表。
        """
        try:
            # TODO: replace with real ORM query when Summary model exists
            return []
        except Exception:
            return []

    # --- 阅读进度字段与并发控制（ETag/If-Match） ---
    def _normalize_state(self, db_session: SessionModel) -> dict:
        """Return a dict state from reading_state (dict or JSON string), safe fallback {}."""
        try:
            state = db_session.reading_state
            if isinstance(state, dict):
                return dict(state)
            if isinstance(state, str):
                return json.loads(state) if state else {}
        except Exception:
            pass
        return {}

    def read_progress_percent(self, db_session: SessionModel) -> int:
        """Return progressPercent from reading_state; default 0."""
        try:
            state = self._normalize_state(db_session)
            val = state.get("progressPercent", 0)
            # allow int-like strings
            if isinstance(val, (int, float)):
                return int(val)
            if isinstance(val, str) and val.isdigit():
                return int(val)
            return 0
        except Exception:
            return 0

    def read_progress_version(self, db_session: SessionModel) -> Union[str, None]:
        """Return current progressVersion (ETag) from reading_state."""
        state = self._normalize_state(db_session)
        ver = state.get("progressVersion")
        return str(ver) if ver else None

    def read_progress_meta(self, db_session: SessionModel) -> dict:
        """Return progress metadata: version, updatedAt, lastSource, progressPercent."""
        state = self._normalize_state(db_session)
        return {
            "progress": int(state.get("progressPercent", 0)) if str(state.get("progressPercent", "0")).isdigit() else 0,
            "version": state.get("progressVersion"),
            "updatedAt": state.get("updatedAt"),
            "lastSource": state.get("lastSource") or {},
        }

    def write_progress_with_meta(
        self,
        db: Session,
        db_session: SessionModel,
        *,
        progress: int,
        device_id: Union[str, None],
        user_agent: Union[str, None],
    ) -> SessionModel:
        """Update progress with new version/updatedAt/lastSource and persist."""
        state = self._normalize_state(db_session)
        state["progressPercent"] = int(progress)
        # new version UUIDv4
        try:
            import uuid as _uuid
            state["progressVersion"] = str(_uuid.uuid4())
        except Exception:
            # extremely unlikely; fallback to random-like string
            state["progressVersion"] = f"v-{int(progress)}-{id(db_session)}"
        # updatedAt ISO8601 (UTC, Z)
        from datetime import datetime, timezone
        state["updatedAt"] = datetime.now(tz=timezone.utc).replace(microsecond=0).isoformat().replace("+00:00", "Z")
        # lastSource
        src = {}
        if device_id:
            src["deviceId"] = device_id
        if user_agent:
            src["userAgent"] = user_agent
        state["lastSource"] = src

        db_session.reading_state = state
        db.add(db_session)
        db.commit()
        db.refresh(db_session)
        return db_session
    
    def _generate_title(self, text: str) -> str:
        """Generate session title from text content."""
        if not text or not text.strip():
            return "未命名会话"
        first_line = text.split('\n')[0].strip()
        if first_line:
            title = first_line[:50]
            if len(first_line) > 50:
                title += "..."
            return title
        title = text.strip()[:50]
        if len(text.strip()) > 50:
            title += "..."
        return title if title else "未命名会话"


# Create singleton instance
session_crud = SessionCRUD()