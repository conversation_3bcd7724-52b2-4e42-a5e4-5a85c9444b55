from typing import List
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.responses import JSONResponse
from app.core.errors import ErrorResponse, ErrorCode
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.core.security import get_user_id_with_fallback
from app.core.errors import ErrorCode
from app.models.schemas import (
    SessionCreate,
    SessionResponse,
    SessionListResponse,
    SessionDetailResponse,
    SessionProgressResponse,
    SessionProgressUpdateRequest,
    MessageCreate,
    MessageResponse,
    ChatCompletionResponse,
)
from app.services.sessions import session_service

router = APIRouter()


@router.post(
    "/sessions",
    response_model=SessionResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建新的学习会话",
    description="接收文本内容，生成标题并创建新的学习会话"
)
async def create_session(
    request: Request,
    session_data: SessionCreate,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_user_id_with_fallback)
) -> SessionResponse:
    """Create a new learning session from text content."""
    try:
        user_uuid = UUID(user_id)
        session_response = session_service.create_session(
            db=db,
            session_data=session_data,
            user_id=user_uuid
        )
        return session_response
    except ValueError:
        trace_id = getattr(request.state, "trace_id", None)
        err = ErrorResponse(
            code=ErrorCode.INVALID_INPUT,
            message="无效的用户ID格式。",
            details={"field": "user_id", "reason": "invalid_uuid"},
            trace_id=trace_id,
        )
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=err.to_dict(),
            headers={"X-Trace-Id": trace_id or ""},
        )
    except Exception:
        trace_id = getattr(request.state, "trace_id", None)
        err = ErrorResponse(
            code=ErrorCode.INTERNAL_ERROR,
            message="创建会话时发生错误，请稍后重试。",
            details={},
            trace_id=trace_id,
        )
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=err.to_dict(),
            headers={"X-Trace-Id": trace_id or ""},
        )


@router.get(
    "/sessions",
    response_model=SessionListResponse,
    summary="获取用户会话列表",
    description="获取当前用户的所有学习会话，按创建时间降序排列"
)
async def get_sessions(
    request: Request,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_user_id_with_fallback)
) -> SessionListResponse:
    """Get user sessions with pagination."""
    
    try:
        # Convert user_id to UUID
        user_uuid = UUID(user_id)

        # Cap limit to a reasonable maximum to avoid large payloads
        MAX_LIMIT = 50
        safe_limit = min(max(limit, 0), MAX_LIMIT)

        # Get sessions through service layer
        sessions_response = session_service.get_user_sessions(
            db=db,
            user_id=user_uuid,
            skip=skip,
            limit=safe_limit
        )

        return sessions_response
        
    except ValueError:
        trace_id = getattr(request.state, "trace_id", None)
        err = ErrorResponse(
            code=ErrorCode.INVALID_INPUT,
            message="无效的用户ID格式。",
            details={"field": "user_id", "reason": "invalid_uuid"},
            trace_id=trace_id,
        )
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=err.to_dict(),
            headers={"X-Trace-Id": trace_id or ""},
        )
    except Exception:
        trace_id = getattr(request.state, "trace_id", None)
        err = ErrorResponse(
            code=ErrorCode.INTERNAL_ERROR,
            message="获取会话列表时发生错误，请稍后重试。",
            details={},
            trace_id=trace_id,
        )
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=err.to_dict(),
            headers={"X-Trace-Id": trace_id or ""},
        )


@router.get(
    "/sessions/{session_id}",
    response_model=SessionDetailResponse,
    summary="获取特定会话详情",
    description="根据会话ID获取会话详细信息（含用于 Reader 的最小内容）"
)
async def get_session(
    request: Request,
    session_id: UUID,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_user_id_with_fallback)
) -> SessionDetailResponse:
    """Get a specific session by ID with minimal Reader content."""
    try:
        user_uuid = UUID(user_id)
        session_response = session_service.get_session_by_id(
            db=db,
            session_id=session_id,
            user_id=user_uuid
        )
        if not session_response:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会话不存在或无权限访问。"
            )
        return session_response
    except HTTPException:
        raise
    except ValueError:
        trace_id = getattr(request.state, "trace_id", None)
        err = ErrorResponse(
            code=ErrorCode.INVALID_INPUT,
            message="无效的用户ID格式。",
            details={"field": "user_id", "reason": "invalid_uuid"},
            trace_id=trace_id,
        )
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=err.to_dict(),
            headers={"X-Trace-Id": trace_id or ""},
        )
    except Exception:
        trace_id = getattr(request.state, "trace_id", None)
        err = ErrorResponse(
            code=ErrorCode.INTERNAL_ERROR,
            message="获取会话详情时发生错误，请稍后重试。",
            details={},
            trace_id=trace_id,
        )
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=err.to_dict(),
            headers={"X-Trace-Id": trace_id or ""},
        )


@router.get(
    "/sessions/{session_id}/progress",
    response_model=SessionProgressResponse,
    summary="获取会话阅读进度（百分比）"
)
async def get_session_progress(
    request: Request,
    session_id: UUID,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_user_id_with_fallback)
) -> JSONResponse:
    try:
        user_uuid = UUID(user_id)
        # 获取业务响应体
        resp_model = session_service.get_session_progress(
            db=db, session_id=session_id, user_id=user_uuid
        )
        # 读取当前服务器端的 meta，用作响应体与 ETag
        meta = session_service.get_progress_version_meta(db=db, session_id=session_id, user_id=user_uuid)
        etag = meta.get("version") or ""
        trace_id = getattr(request.state, "trace_id", None)
        body = resp_model.model_dump()
        # 将 meta 并入 body（保持键名：updatedAt, version, lastSource）
        body["meta"] = {
            "updatedAt": meta.get("updatedAt"),
            "version": meta.get("version"),
            "lastSource": meta.get("lastSource") or {},
        }
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=body,
            headers={
                "X-Trace-Id": trace_id or "",
                "ETag": etag,
            },
        )
    except HTTPException:
        raise
    except ValueError:
        trace_id = getattr(request.state, "trace_id", None)
        err = ErrorResponse(
            code=ErrorCode.INVALID_INPUT,
            message="无效的用户或会话ID。",
            details={"field": "user_id/session_id", "reason": "invalid_uuid"},
            trace_id=trace_id,
        )
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=err.to_dict(),
            headers={"X-Trace-Id": trace_id or ""},
        )
    except Exception:
        trace_id = getattr(request.state, "trace_id", None)
        err = ErrorResponse(
            code=ErrorCode.INTERNAL_ERROR,
            message="获取阅读进度时发生错误。",
            details={},
            trace_id=trace_id,
        )
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=err.to_dict(),
            headers={"X-Trace-Id": trace_id or ""},
        )


@router.put(
    "/sessions/{session_id}/progress",
    response_model=SessionProgressResponse,
    summary="更新会话阅读进度（百分比）"
)
async def update_session_progress(
    request: Request,
    session_id: UUID,
    payload: SessionProgressUpdateRequest,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_user_id_with_fallback)
) -> JSONResponse:
    try:
        user_uuid = UUID(user_id)
        # 读取 If-Match 与来源信息
        if_match = request.headers.get("If-Match")
        device_id = request.headers.get("X-Device-Id")
        user_agent = request.headers.get("User-Agent")

        resp_model, new_version = session_service.update_session_progress(
            db=db,
            session_id=session_id,
            user_id=user_uuid,
            progress=payload.progress,
            if_match=if_match,
            device_id=device_id,
            user_agent=user_agent,
        )
        trace_id = getattr(request.state, "trace_id", None)
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=resp_model.model_dump(),
            headers={
                "X-Trace-Id": trace_id or "",
                "ETag": new_version or "",
            },
        )
    except HTTPException as he:
        # 自定义处理 428/409，保持响应结构与故事契约一致
        trace_id = getattr(request.state, "trace_id", None)
        if he.status_code == 428:
            err = {
                "error": "precondition_required",
                "message": "If-Match header required for concurrency control",
            }
            return JSONResponse(
                status_code=428,
                content=err,
                headers={"X-Trace-Id": trace_id or ""},
            )
        if he.status_code == status.HTTP_409_CONFLICT:
            # he.detail 由 service 构造，包含 server 字段
            body = he.detail if isinstance(he.detail, dict) else {"error": "conflict"}
            return JSONResponse(
                status_code=status.HTTP_409_CONFLICT,
                content=body,
                headers={"X-Trace-Id": trace_id or ""},
            )
        raise
    except ValueError:
        trace_id = getattr(request.state, "trace_id", None)
        err = ErrorResponse(
            code=ErrorCode.INVALID_INPUT,
            message="无效的参数。",
            details={"field": "user_id/session_id/progress", "reason": "invalid_value"},
            trace_id=trace_id,
        )
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=err.to_dict(),
            headers={"X-Trace-Id": trace_id or ""},
        )
    except Exception:
        trace_id = getattr(request.state, "trace_id", None)
        err = ErrorResponse(
            code=ErrorCode.INTERNAL_ERROR,
            message="更新阅读进度时发生错误。",
            details={},
            trace_id=trace_id,
        )
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=err.to_dict(),
            headers={"X-Trace-Id": trace_id or ""},
        )


@router.post(
    "/sessions/{session_id}/messages",
    response_model=ChatCompletionResponse,
    status_code=status.HTTP_201_CREATED,
    summary="发送消息并获取AI回复",
    description="向指定会话发送用户消息，获取AI回复，并可能更新摘要"
)
async def send_message(
    request: Request,
    session_id: UUID,
    message_data: MessageCreate,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_user_id_with_fallback)
) -> ChatCompletionResponse:
    """Send a message to a session and get AI response."""
    try:
        user_uuid = UUID(user_id)

        # Import here to avoid circular imports
        from app.services.messages import message_service

        response = await message_service.send_message(
            db=db,
            session_id=session_id,
            user_id=user_uuid,
            content=message_data.content
        )

        return response

    except ValueError:
        trace_id = getattr(request.state, "trace_id", None)
        err = ErrorResponse(
            code=ErrorCode.INVALID_INPUT,
            message="无效的用户ID格式。",
            details={"field": "user_id", "reason": "invalid_uuid"},
            trace_id=trace_id,
        )
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=err.to_dict(),
            headers={"X-Trace-Id": trace_id or ""},
        )
    except HTTPException:
        raise
    except Exception as e:
        trace_id = getattr(request.state, "trace_id", None)
        err = ErrorResponse(
            code=ErrorCode.INTERNAL_ERROR,
            message="发送消息时发生错误，请稍后重试。",
            details={"error": str(e)},
            trace_id=trace_id,
        )
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=err.to_dict(),
            headers={"X-Trace-Id": trace_id or ""},
        )