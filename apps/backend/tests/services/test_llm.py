"""
LLM Service Tests

Tests for the LLM service adapter and OpenAI SDK integration.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from app.services.llm import LLMService, LLMMessage, LLMResponse, OpenAILibraryAdapter
from app.core.errors import A<PERSON><PERSON>rror, ErrorCode


class TestLLMMessage:
    """Test LLMMessage data class."""
    
    def test_create_message(self):
        """Test creating a basic LLM message."""
        message = LLMMessage(role="user", content="Hello, world!")
        assert message.role == "user"
        assert message.content == "Hello, world!"
        assert message.metadata is None
    
    def test_create_message_with_metadata(self):
        """Test creating a message with metadata."""
        metadata = {"source": "test", "timestamp": "2025-08-07"}
        message = LLMMessage(role="assistant", content="Hi there!", metadata=metadata)
        assert message.role == "assistant"
        assert message.content == "Hi there!"
        assert message.metadata == metadata


class TestLLMResponse:
    """Test LLMResponse data class."""
    
    def test_create_response(self):
        """Test creating a basic LLM response."""
        response = LLMResponse(content="Hello!", model="gpt-4o-mini")
        assert response.content == "Hello!"
        assert response.model == "gpt-4o-mini"
        assert response.usage is None
        assert response.metadata is None
    
    def test_create_response_with_usage(self):
        """Test creating a response with usage information."""
        usage = {"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15}
        response = LLMResponse(content="Hello!", model="gpt-4o-mini", usage=usage)
        assert response.usage == usage


class TestOpenAILibraryAdapter:
    """Test OpenAI Library Adapter."""
    
    @pytest.fixture
    def adapter(self):
        """Create a test adapter instance."""
        return OpenAILibraryAdapter(
            api_key="test-key",
            api_base="https://api.openai.com/v1",
            timeout_ms=20000
        )
    
    def test_adapter_initialization(self, adapter):
        """Test adapter initialization."""
        assert adapter.api_key == "test-key"
        assert adapter.api_base == "https://api.openai.com/v1"
        assert adapter.timeout_ms == 20000
    
    def test_format_messages(self, adapter):
        """Test message formatting."""
        messages = [
            LLMMessage(role="user", content="Hello"),
            LLMMessage(role="assistant", content="Hi there!")
        ]
        formatted = adapter._format_messages(messages)
        expected = [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi there!"}
        ]
        assert formatted == expected
    
    @pytest.mark.asyncio
    async def test_chat_completion_non_streaming(self, adapter):
        """Test non-streaming chat completion."""
        messages = [LLMMessage(role="user", content="Hello")]
        
        # Mock the OpenAI client
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = "Hello there!"
        mock_response.usage = MagicMock()
        mock_response.usage.prompt_tokens = 5
        mock_response.usage.completion_tokens = 3
        mock_response.usage.total_tokens = 8
        
        with patch.object(adapter._client.chat.completions, 'create', new_callable=AsyncMock) as mock_create:
            mock_create.return_value = mock_response
            
            result = await adapter.chat_completion(messages, model="gpt-4o-mini", stream=False)
            
            assert isinstance(result, LLMResponse)
            assert result.content == "Hello there!"
            assert result.model == "gpt-4o-mini"
            assert result.usage == {
                "prompt_tokens": 5,
                "completion_tokens": 3,
                "total_tokens": 8
            }
            
            # Verify the client was called correctly
            mock_create.assert_called_once()
            call_args = mock_create.call_args
            assert call_args[1]["model"] == "gpt-4o-mini"
            assert call_args[1]["messages"] == [{"role": "user", "content": "Hello"}]
            assert call_args[1]["stream"] is False
    
    @pytest.mark.asyncio
    async def test_chat_completion_error_handling(self, adapter):
        """Test error handling in chat completion."""
        messages = [LLMMessage(role="user", content="Hello")]
        
        with patch.object(adapter._client.chat.completions, 'create', new_callable=AsyncMock) as mock_create:
            mock_create.side_effect = Exception("API Error")
            
            with pytest.raises(AppError) as exc_info:
                await adapter.chat_completion(messages, model="gpt-4o-mini", stream=False)
            
            assert exc_info.value.error_code == ErrorCode.EXTERNAL_SERVICE_ERROR
            assert "OpenAI SDK error" in exc_info.value.message


class TestLLMService:
    """Test LLM Service."""
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        with patch('app.services.llm.settings') as mock_settings:
            mock_settings.openai_api_key = "test-key"
            mock_settings.openai_base_url = "https://api.openai.com/v1"
            mock_settings.openai_org = None
            mock_settings.openai_timeout_ms = 20000
            mock_settings.llm_request_timeout_ms = 20000
            mock_settings.external_max_retries = 2
            mock_settings.retry_backoff_initial_ms = 500
            mock_settings.retry_backoff_max_ms = 8000
            mock_settings.openai_model = "gpt-4o-mini"
            yield mock_settings
    
    def test_service_initialization_success(self, mock_settings):
        """Test successful service initialization."""
        service = LLMService()
        assert service.adapter is not None
        assert isinstance(service.adapter, OpenAILibraryAdapter)
    
    def test_service_initialization_no_api_key(self):
        """Test service initialization without API key."""
        with patch('app.services.llm.settings') as mock_settings:
            mock_settings.openai_api_key = None
            mock_settings.llm_api_key = None
            
            service = LLMService()
            assert service.adapter is None
    
    @pytest.mark.asyncio
    async def test_chat_completion_no_adapter(self):
        """Test chat completion when no adapter is configured."""
        service = LLMService()
        service.adapter = None
        
        messages = [LLMMessage(role="user", content="Hello")]
        
        with pytest.raises(AppError) as exc_info:
            await service.chat_completion(messages)
        
        assert exc_info.value.error_code == ErrorCode.SERVICE_UNAVAILABLE
        assert "LLM service not configured" in exc_info.value.message
    
    @pytest.mark.asyncio
    async def test_chat_completion_success(self, mock_settings):
        """Test successful chat completion."""
        service = LLMService()
        
        # Mock the adapter
        mock_adapter = AsyncMock()
        mock_response = LLMResponse(content="Hello there!", model="gpt-4o-mini")
        mock_adapter.chat_completion.return_value = mock_response
        service.adapter = mock_adapter
        
        messages = [LLMMessage(role="user", content="Hello")]
        result = await service.chat_completion(messages)
        
        assert isinstance(result, LLMResponse)
        assert result.content == "Hello there!"
        assert result.metadata is not None
        assert result.metadata["provider"] in ["openai-sdk", "openai"]  # Both are acceptable
        assert result.metadata["attempts"] == 1
        assert "latency_ms" in result.metadata
    
    @pytest.mark.asyncio
    async def test_chat_completion_retry_logic(self, mock_settings):
        """Test retry logic on failures."""
        service = LLMService()
        
        # Mock the adapter to fail twice, then succeed
        mock_adapter = AsyncMock()
        mock_response = LLMResponse(content="Success!", model="gpt-4o-mini")
        mock_adapter.chat_completion.side_effect = [
            AppError(ErrorCode.EXTERNAL_SERVICE_ERROR, "First failure"),
            AppError(ErrorCode.EXTERNAL_SERVICE_ERROR, "Second failure"),
            mock_response
        ]
        service.adapter = mock_adapter
        
        messages = [LLMMessage(role="user", content="Hello")]
        
        with patch('asyncio.sleep', new_callable=AsyncMock):  # Speed up the test
            result = await service.chat_completion(messages)
        
        assert isinstance(result, LLMResponse)
        assert result.content == "Success!"
        assert mock_adapter.chat_completion.call_count == 3
    
    @pytest.mark.asyncio
    async def test_chat_completion_max_retries_exceeded(self, mock_settings):
        """Test behavior when max retries are exceeded."""
        service = LLMService()
        
        # Mock the adapter to always fail
        mock_adapter = AsyncMock()
        mock_adapter.chat_completion.side_effect = AppError(
            ErrorCode.EXTERNAL_SERVICE_ERROR, "Persistent failure"
        )
        service.adapter = mock_adapter
        
        messages = [LLMMessage(role="user", content="Hello")]
        
        with patch('asyncio.sleep', new_callable=AsyncMock):  # Speed up the test
            with pytest.raises(AppError) as exc_info:
                await service.chat_completion(messages)
        
        assert exc_info.value.error_code == ErrorCode.EXTERNAL_SERVICE_ERROR
        assert "Persistent failure" in exc_info.value.message
        # Should try initial + max_retries times
        assert mock_adapter.chat_completion.call_count == 3  # 1 + 2 retries
