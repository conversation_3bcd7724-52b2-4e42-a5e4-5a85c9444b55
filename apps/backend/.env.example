# Backend .env.example (reference-only)
# 重要：为避免重复维护，环境变量以仓库根目录的 .env / .env.example 为单一事实来源（Single Source of Truth）。
# 本文件仅提供后端最小必要说明；请在根目录维护 .env，并通过 Make 目标同步生成 apps/backend/.env。

# 使用方法：
#   1) 在仓库根目录：
#        cp .env.example .env
#        # 按需填写 OPENAI_API_KEY / OPENAI_BASE_URL / LLM_DEFAULT_MODEL 等键
#   2) 同步到后端：
#        make backend-env-sync
#      该命令会基于根 .env 生成 apps/backend/.env（仅包含后端所需键）。
#   3) 启动后端（需先完成 venv 与依赖安装）：
#        make dev
#
# 后端关键键（在根 .env 维护，已精简为三项必填 + 可选）：
#   - OPENAI_API_KEY      # 必填
#   - OPENAI_BASE_URL     # 必填
#   - OPENAI_MODEL        # 必填（gpt-4o / gpt-4o-mini / o4-mini 等）
#   - OPENAI_TIMEOUT      # 可选（毫秒）
#
# 如需完全独立部署后端，可直接在 apps/backend/.env 维护同名键，但团队标准推荐通过根 .env 同步生成，避免重复编辑。