# 后端开发指南

## 环境要求

- **Python 3.12+** （必需，使用新语法特性如 `str | None`）
- **uv** （现代化 Python 包管理器）
- **PostgreSQL** （通过 Supabase 或本地安装）

## 快速开始

### 1. 安装 uv
```bash
# 安装 uv（如果尚未安装）
curl -LsSf https://astral.sh/uv/install.sh | sh
# 或使用 pip: pip install uv

# 验证安装
uv --version
```

### 2. 环境设置
```bash
# 确保使用 Python 3.12+
python3 --version  # 应显示 Python 3.12.x

# 如果版本不符，使用 pyenv 安装
# pyenv install 3.12.0
# pyenv local 3.12.0
```

### 3. 依赖安装
```bash
cd apps/backend

# uv 会自动创建虚拟环境并安装依赖
uv pip install -r requirements.txt

# 验证安装
uv run python -c "import openai; print('OpenAI SDK installed successfully')"
```

### 4. 环境配置
```bash
# 从根目录复制环境变量模板
cp ../../.env.example .env

# 编辑 .env 文件，至少配置以下必需项：
# - DATABASE_URL
# - OPENAI_API_KEY
# - OPENAI_BASE_URL
# - OPENAI_MODEL
```

### 5. 启动开发服务器
```bash
# 启动 FastAPI 开发服务器
uv run uvicorn app.main:app --reload

# 服务器将在 http://localhost:8000 启动
# API 文档：http://localhost:8000/docs
```

## 开发工作流

### 运行测试
```bash
# 运行所有测试
uv run pytest

# 运行特定测试文件
uv run pytest tests/test_llm.py

# 运行测试并生成覆盖率报告
uv run pytest --cov=app tests/

# 详细输出模式
uv run pytest -v

# 监听文件变化并自动运行测试
uv run pytest-watch
```

### 代码质量检查
```bash
# 代码格式化（如果配置了 black）
uv run black app/ tests/

# 代码检查（如果配置了 ruff）
uv run ruff check app/ tests/

# 类型检查（如果配置了 mypy）
uv run mypy app/
```

### 数据库操作
```bash
# 生成数据库迁移
uv run alembic revision --autogenerate -m "描述变更"

# 应用数据库迁移
uv run alembic upgrade head

# 查看迁移历史
uv run alembic history
```

## 项目结构

```
apps/backend/
├── app/
│   ├── api/           # API 路由和端点
│   ├── core/          # 核心配置、错误处理、日志
│   ├── crud/          # 数据库 CRUD 操作
│   ├── db/            # 数据库连接和会话
│   ├── models/        # SQLAlchemy 模型和 Pydantic 模式
│   ├── services/      # 业务逻辑服务（如 LLM 服务）
│   └── main.py        # FastAPI 应用入口
├── tests/             # 测试文件
├── requirements.txt   # Python 依赖
├── .env              # 环境变量（不提交到版本控制）
└── README.md         # 本文件
```

## 重要说明

### Python 版本兼容性
- 项目使用 Python 3.12+ 的新语法特性（如 `str | None` 联合类型）
- 不兼容 Python 3.11 及以下版本
- 建议使用 pyenv 管理 Python 版本

### uv 包管理器优势
- **极快的安装速度**：比 pip 快 10-100 倍
- **自动虚拟环境管理**：无需手动创建和激活 venv
- **更好的依赖解析**：避免依赖冲突
- **兼容 pip**：可以无缝替换现有的 pip 工作流

### 环境变量管理
- 使用根目录的 `.env` 作为单一事实来源
- 后端 `.env` 文件通过 `make backend-env-sync` 同步生成
- 不要在 shell 中全局 export 机密变量

## 故障排除

### 常见问题

1. **Python 版本错误**
   ```bash
   # 检查当前 Python 版本
   python3 --version
   
   # 使用 pyenv 切换版本
   pyenv install 3.12.0
   pyenv local 3.12.0
   ```

2. **依赖安装失败**
   ```bash
   # 清理缓存并重新安装
   uv cache clean
   uv pip install -r requirements.txt --refresh
   ```

3. **数据库连接错误**
   ```bash
   # 检查 DATABASE_URL 配置
   echo $DATABASE_URL
   
   # 测试数据库连接
   uv run python -c "from app.db.session import engine; print(engine.url)"
   ```

4. **OpenAI API 错误**
   ```bash
   # 检查 API 密钥配置
   echo $OPENAI_API_KEY
   
   # 测试 API 连接
   uv run python -c "from app.services.llm import llm_service; print('LLM service initialized')"
   ```

## 贡献指南

1. 确保使用 Python 3.12+ 和 uv 包管理器
2. 运行测试确保所有测试通过：`uv run pytest`
3. 遵循代码风格规范
4. 更新相关文档
5. 提交前运行完整的测试套件

## 相关文档

- [项目根目录 README](../../README.md)
- [架构文档](../../docs/architecture.md)
- [API 文档](../../docs/openapi.yaml)
- [测试策略](../../docs/testing-strategy-guide.md)
