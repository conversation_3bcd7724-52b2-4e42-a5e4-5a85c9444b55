import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import { MemoryRouter, Routes, Route } from "react-router-dom";
import Reader from "./Reader";
import * as api from "../api/client";
import { summaryTexts } from "../constants/ui";

// 使用全局的 __advanceAndFlush__ 函数（在 test/setup.ts 提供）
declare global {
  // eslint-disable-next-line no-var
  var __advanceAndFlush__: (ms?: number) => Promise<void>;
}

describe("Reader 摘要更新调试", () => {
  beforeEach(() => {
    vi.useFakeTimers();
    vi.clearAllMocks();
  });

  it.skip("调试摘要更新功能", async () => {
    const first = {
      id: "s1",
      title: "T",
      content: { paragraphs: [{ index: 0, text: "p0" }] },
      summary_latest: null,
    } as any;

    const second = {
      id: "s1",
      title: "T",
      content: { paragraphs: [{ index: 0, text: "p0" }] },
      summary_latest: { text: "summary-updated" },
    } as any;

    const spy = vi.spyOn(api, "getSessionById")
      .mockResolvedValueOnce(first)
      .mockResolvedValueOnce(second);

    const { rerender } = render(
      <MemoryRouter initialEntries={["/reader/s1"]}>
        <Routes>
          <Route path="/reader/:id" element={<Reader />} />
        </Routes>
      </MemoryRouter>
    );

    await __advanceAndFlush__(500);

    await waitFor(() => {
      expect(screen.queryByText("p0")).toBeInTheDocument();
    }, { timeout: 2000 });

    // 重新渲染以触发第二次 API 调用
    rerender(
      <MemoryRouter initialEntries={["/reader/s1"]}>
        <Routes>
          <Route path="/reader/:id" element={<Reader />} />
        </Routes>
      </MemoryRouter>
    );

    await __advanceAndFlush__(500);

    // 提示应该出现
    await waitFor(() => {
      expect(screen.queryByText(new RegExp(summaryTexts.updated))).toBeInTheDocument();
    }, { timeout: 3000, interval: 100 });

    // 推进 2 秒等待提示隐藏
    await __advanceAndFlush__(2200);

    await waitFor(() => {
      expect(screen.queryByText(new RegExp(summaryTexts.updated))).toBeNull();
    }, { timeout: 3000, interval: 100 });

    expect(spy.mock.calls.length).toBeGreaterThanOrEqual(2);
    spy.mockRestore();
  }, 20000);
});
