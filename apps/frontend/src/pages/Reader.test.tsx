import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import { MemoryRouter, Routes, Route } from "react-router-dom";
import Reader from "./Reader";
import * as api from "../api/client";
import { summaryTexts } from "../constants/ui";

// 使用全局推进工具
declare global {
  // eslint-disable-next-line no-var
  var __advanceAndFlush__: (ms?: number) => Promise<void>;
}

describe("Reader 页面 - 重试/摘要提示/长加载提示", () => {
  const TEST_TIMEOUT = 10_000;

  beforeEach(() => {
    vi.useFakeTimers();
    vi.setSystemTime(new Date("2025-01-01T00:00:00.000Z"));

    // stub 基础数据，避免 Reader 首载阶段卡住
    vi.spyOn(api, "getSessionById").mockResolvedValue({
      id: "s1",
      title: "T",
      content: { paragraphs: [{ index: 0, text: "p0" }] },
      // 为触发“摘要更新提示”，提供一个初始 summary_latest
      summary_latest: null,
    } as any);
    vi.spyOn(api, "getSessionProgress").mockResolvedValue({
      session_id: "s1",
      progress: 0,
      _meta: { etag: "v" },
      meta: { updatedAt: "2025-01-01T00:00:00.000Z" },
    } as any);

    // 抑制副作用，避免干扰测试
    vi.spyOn(api, "updateSessionProgress").mockResolvedValue({
      session_id: "s1",
      progress: 0,
      _meta: { etag: "v" },
    } as any);
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  it(
    "长加载时显示 aria-live 提示",
    async () => {
      // 对于这个测试，我们不使用 fake timers，让长加载逻辑在真实时间中运行
      vi.useRealTimers();

      // 用可控延迟模拟“首个详情请求”卡住，从而触发长加载提示
      const deferred: { resolve?: (v: any) => void } = {};
      const sessionPromise = new Promise<any>((resolve) => {
        deferred.resolve = resolve;
      });
      // 重新 mock getSessionById 为永不 resolve 的 Promise
      vi.mocked(api.getSessionById).mockReturnValue(sessionPromise as Promise<any>);

      render(
        <MemoryRouter initialEntries={["/reader/s1"]}>
          <Routes>
            <Route path="/reader/:id" element={<Reader />} />
          </Routes>
        </MemoryRouter>
      );

      // 等待长加载提示出现，使用真实时间（longLoadingMs = 10000ms）
      // 给予足够的时间让长加载逻辑触发

      // 直接查找“仍在加载”提示（Reader.tsx 使用 role="status" + aria-live）
      // 使用 queryByRole 以避免 getByRole 在提示尚未出现时立即抛错
      const longLoadingElement = await screen.findByTestId(
        "loading-long-status",
        {},
        { timeout: 15000 }
      );
      expect(longLoadingElement).toBeInTheDocument();
      expect(longLoadingElement.textContent || "").toMatch(/仍在加载|请稍候|加载较久/);

      // 结束 pending，避免后续泄漏
      deferred.resolve?.({
        id: "s1",
        title: "T",
        content: { paragraphs: [{ index: 0, text: "p0" }] },
        summary_latest: null,
      });

      // 等待组件状态更新完成
      await waitFor(() => {
        expect(screen.queryByTestId("loading-long-status")).not.toBeInTheDocument();
      });
    },
    20_000
  );

  it.skip(
    "错误后自动重试失败再显示错误，手动重试后成功",
    async () => {
      // 当前 Reader 组件未实现 sendMessage/对话输入，此用例在 MVP Reader 中暂不适用，后续在对话视图落地后恢复
    },
    TEST_TIMEOUT
  );

  it.skip(
    "收到摘要更新时，出现并短暂显示提示 - 暂时跳过，需要重新设计测试逻辑",
    async () => {
      // 对于这个测试，我们也使用真实时间
      vi.useRealTimers();
      const dataWithSummary = {
        id: "s1",
        title: "T",
        content: { paragraphs: [{ index: 0, text: "p0" }] },
        summary_latest: { text: "summary-updated" },
      } as any;

      // 直接 mock 返回有摘要的数据，这应该触发摘要更新提示
      const spy = vi.spyOn(api, "getSessionById")
        .mockResolvedValue(dataWithSummary);

      render(
        <MemoryRouter initialEntries={["/reader/s1"]}>
          <Routes>
            <Route path="/reader/:id" element={<Reader />} />
          </Routes>
        </MemoryRouter>
      );

      // 等待首次加载完成
      await waitFor(() => {
        expect(screen.queryByText("加载中")).not.toBeInTheDocument();
      }, { timeout: 5000 });

      // 重新渲染以模拟“刷新后加载第二次数据”
      rerender(
        <MemoryRouter initialEntries={["/reader/s1"]}>
          <Routes>
            <Route path="/reader/:id" element={<Reader />} />
          </Routes>
        </MemoryRouter>
      );

      // 等待第二次加载完成并触发摘要更新提示

      // 提示应该出现（使用 testid）
      await waitFor(
        () => {
          const hint = screen.getByTestId("summary-updated-banner");
          expect(hint).toBeInTheDocument();
          expect(hint.textContent || "").toMatch(new RegExp(summaryTexts.updated));
        },
        { timeout: 3000, interval: 100 }
      );

      // 等待提示自动隐藏（真实时间，大约2秒后隐藏）

      await waitFor(
        () => {
          expect(screen.queryByTestId("summary-updated-banner")).toBeNull();
        },
        { timeout: 5000, interval: 200 }
      );

      spy.mockRestore();
    },
    20000
  );
});